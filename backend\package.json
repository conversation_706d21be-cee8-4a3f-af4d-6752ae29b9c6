{"name": "event-driven-backend", "version": "1.0.0", "description": "Backend microservice with event-driven architecture", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "joi": "^17.11.0", "mongoose": "^8.0.3", "socket.io": "^4.7.4", "redis": "^4.6.10", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "uuid": "^9.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/index.js"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}}