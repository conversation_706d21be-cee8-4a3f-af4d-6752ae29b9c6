const express = require('express');
const userController = require('../controllers/userController');
const { validateUser, validateUserUpdate } = require('../middleware/validation');

const router = express.Router();

// Public routes
router.post('/register', validateUser, userController.register);
router.post('/login', userController.login);
router.post('/forgot-password', userController.forgotPassword);
router.patch('/reset-password/:token', userController.resetPassword);

// Protected routes (require authentication)
// router.use(authController.protect); // We'll implement this later

// User profile routes
router.get('/profile', userController.getProfile);
router.patch('/profile', validateUserUpdate, userController.updateProfile);
router.delete('/profile', userController.deleteProfile);
router.patch('/change-password', userController.changePassword);

// Admin routes
// router.use(authController.restrictTo('admin')); // We'll implement this later

router.get('/', userController.getAllUsers);
router.get('/stats', userController.getUserStats);
router.get('/:id', userController.getUser);
router.patch('/:id', validateUserUpdate, userController.updateUser);
router.delete('/:id', userController.deleteUser);

module.exports = router;
