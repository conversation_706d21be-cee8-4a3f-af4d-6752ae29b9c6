{"name": "event-driven-microservice", "version": "1.0.0", "description": "A microservice with event-driven architecture using Node.js and React", "main": "index.js", "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run frontend:dev\"", "backend:dev": "cd backend && npm run dev", "frontend:dev": "cd frontend && npm run dev", "backend:start": "cd backend && npm start", "frontend:start": "cd frontend && npm start", "backend:test": "cd backend && npm test", "frontend:test": "cd frontend && npm test", "test": "npm run backend:test && npm run frontend:test", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install"}, "keywords": ["microservice", "event-driven", "nodejs", "react", "architecture"], "author": "Developer", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}